# Microsoft Store 支付集成 Demo 使用指南

## 环境准备
- **开发工具**：Visual Studio 2022 或更高版本
- **项目文件**：`PhotoViewer.sln`
- **账户要求**：有效的Microsoft开发者账户（需应用管理权限）

## 操作步骤

### 1. 打开解决方案
双击或通过VS2022打开 `PhotoViewer.sln` 解决方案文件

### 2. 关联 Store 应用
> **说明**：默认已关联红鲸科技 Store 应用 ID（可选自定义）
1. 在解决方案资源管理器：
   - 右键单击 `PhotoViewer.Package` 项目
   - 选择 **发布** > **将应用程序与应用商店关联**
   
### 3. 启动关联向导
在打开的关联向导中点击 **下一步**：
![关联向导界面](image-20250716111617067.png)

### 4. 登录开发者门户
1. 访问 [Microsoft 合作伙伴中心](https://partner.microsoft.com/dashboard/v2/home)
2. 使用开发者账户登录：
![合作伙伴中心登录](image-20250716111754368.png)

### 5. 选择目标应用
在应用列表中选择需关联的应用程序后点击 **下一步**：
![应用选择界面](image-20250716112143368.png)

### 6. 完成关联操作
在确认页面点击 **关联** 按钮：
> **技术说明**：此操作将自动配置 `StoreContext` 上下文
![关联确认界面](image-20250716112345383.png)

### 7. 验证支付功能
1. 编译并运行应用程序 (F5)
2. 导航至 **个人中心** 页面
3. 确认应用内商品列表正常加载：
   ```plaintext
   - 永久VIP: $0.00
   - 图片编辑VIP: $0.00
   - 游戏币: $0.00

![image-20250716114250332](image-20250716114250332.png)

