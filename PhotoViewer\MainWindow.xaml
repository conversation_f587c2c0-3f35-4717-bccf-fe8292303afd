﻿<Window x:Class="PhotoViewer.MainWindow"
       xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
       xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
       xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
       xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
       xmlns:local="clr-namespace:PhotoViewer"
       mc:Ignorable="d"
       WindowStartupLocation="CenterScreen"
       Title="iPhotoView" Height="700" Width="1000"
       SizeChanged="Window_SizeChanged">
    <Window.Resources>
        <Style x:Key="ToolbarButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderBrush" Value="Transparent" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="Padding" Value="5" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#f1f1f1" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#e0e0e0" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="ToolBar">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToolBar">
                        <Border Background="{TemplateBinding Background}" Padding="2">
                            <StackPanel Orientation="Horizontal" IsItemsHost="True" />
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    <DockPanel>
        <!-- 菜单栏固定在顶部 -->
        <Menu DockPanel.Dock="Top">
            <MenuItem Header="文件">
                <MenuItem Header="打开" Click="OpenButton_Click" />
                <MenuItem Header="关闭" Click="CloseImage_Click" />
                <Separator />
                <MenuItem Header="格式关联" Click="MenuFormatAssociation_Click" />
                <Separator />
                <MenuItem Header="退出" Click="Exit_Click" />
            </MenuItem>
            <MenuItem Header="查看">
                <MenuItem x:Name="ToggleToolbarMenuItem" Header="隐藏工具栏" Click="ToggleToolbar_Click" />
                <MenuItem x:Name="ToggleStatusbarMenuItem" Header="隐藏状态栏" Click="ToggleStatusbar_Click" />
                <Separator />
                <MenuItem Header="放大" Click="ZoomInButton_Click" />
                <MenuItem Header="缩小" Click="ZoomOutButton_Click" />
                <MenuItem Header="原始大小" Click="OriginalSizeButton_Click" />
                <MenuItem Header="匹配窗口" Click="FitToWindowButton_Click" />
                <Separator />
                <MenuItem Header="全屏" Click="ToggleFullscreen_Click" />
            </MenuItem>
            <MenuItem Header="关于">
                <MenuItem Header="关于" Click="About_Click" />
            </MenuItem>
            <MenuItem Header="服务商城">
                <MenuItem Header="服务商城" Click="Person_Click" />
            </MenuItem>
        </Menu>

        <!-- 工具栏放在菜单栏下方 -->
        <ToolBar x:Name="MainToolbar"
               DockPanel.Dock="Top"
               ToolBarTray.IsLocked="True"
               ToolBar.OverflowMode="Never"
               HorizontalAlignment="Center"
                Background="Transparent">
            <Button ToolTip="打开" Style="{StaticResource ToolbarButtonStyle}" Click="OpenButton_Click">
                <Image Source="/Images/file_open.png" Width="30" />
            </Button>
            <Separator />
            <Button ToolTip="放大" Style="{StaticResource ToolbarButtonStyle}" Click="ZoomInButton_Click">
                <Image Source="/Images/zoom_in.png" Width="30" />
            </Button>
            <Button ToolTip="缩小" Style="{StaticResource ToolbarButtonStyle}" Click="ZoomOutButton_Click">
                <Image Source="/Images/zoom_out.png" Width="30" />
            </Button>
            <Separator />
            <Button ToolTip="原始大小" Style="{StaticResource ToolbarButtonStyle}" Click="OriginalSizeButton_Click">
                <Image Source="/Images/1_1.png" Width="30" />
            </Button>
            <Button ToolTip="匹配窗口" Style="{StaticResource ToolbarButtonStyle}" Click="FitToWindowButton_Click">
                <Image Source="/Images/fit.png" Width="30" />
            </Button>
        </ToolBar>

        <!-- 状态栏固定在底部 -->
        <StatusBar x:Name="MainStatusBar" DockPanel.Dock="Bottom">
            <StatusBarItem>
                <TextBlock x:Name="CursorPositionText" />
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock x:Name="ZoomLevelText" Text="缩放: 100%" />
            </StatusBarItem>
        </StatusBar>

        <!-- 图片显示区域占据剩余空间 -->
        <ScrollViewer Name="ScrollViewer" HorizontalScrollBarVisibility="Hidden" VerticalScrollBarVisibility="Hidden"
                     PreviewMouseWheel="ScrollViewer_PreviewMouseWheel">
            <Image Name="ImageViewer" Stretch="None" MouseMove="ImageViewer_MouseMove" />
        </ScrollViewer>
    </DockPanel>
</Window>