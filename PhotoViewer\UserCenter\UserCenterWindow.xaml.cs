using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Net.Http;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Interop;
using Windows.Services.Store;
using Microsoft.Win32;
using System.IO;

namespace PhotoViewer.UserCenter
{
    /// <summary>
    /// UserCenterWindow.xaml 的交互逻辑
    /// </summary>
    public partial class UserCenterWindow : Window
    {
        /// <summary>
        /// 更多API请查看：https://learn.microsoft.com/zh-cn/uwp/api/windows.services.store.storecontext?view=winrt-26100
        /// </summary>
        private StoreContext _storeContext;

        private static string _baseUrl = "https://192.168.0.35:53804/";

        public ObservableCollection<ProductItem> Products { get; set; }

        /// <summary>
        /// 设置当前用户ID，用于演示。在实际应用中，应从登录状态获取或存储此信息。
        /// </summary>
        public string UserId { get; private set; }

        private static readonly HttpClient _httpClient = new HttpClient(new HttpClientHandler
        {
            ServerCertificateCustomValidationCallback = (msg, cert, chain, errors) => true
        }) 
        {
            BaseAddress = new Uri(_baseUrl)
        };

        private string _PurchaseId = string.Empty;
        private string _CollectionsId = string.Empty;
        private string _CustomerPurchaseId = string.Empty;
        private string _CustomerCollectionsId = string.Empty;

        /// <summary>
        /// 生成8位字母加数字的随机用户名
        /// </summary>
        /// <returns>随机用户名</returns>
        private string GenerateRandomUsername()
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var random = new Random();
            var result = new char[8];

            for (int i = 0; i < 8; i++)
            {
                result[i] = chars[random.Next(chars.Length)];
            }

            return new string(result);
        }

        /// <summary>
        /// 从注册表获取或生成用户名
        /// </summary>
        /// <returns>用户名</returns>
        private string GetOrCreateUserId()
        {
            const string registryKeyPath = @"SOFTWARE\PhotoViewer";
            const string userIdValueName = "UserId";

            try
            {
                // 尝试从注册表读取用户名
                using (var key = Registry.CurrentUser.OpenSubKey(registryKeyPath))
                {
                    if (key != null)
                    {
                        var storedUserId = key.GetValue(userIdValueName) as string;
                        if (!string.IsNullOrEmpty(storedUserId))
                        {
                            return storedUserId;
                        }
                    }
                }

                // 如果没有找到，生成新的用户名并保存
                var newUserId = GenerateRandomUsername();

                // 保存到注册表
                using (var key = Registry.CurrentUser.CreateSubKey(registryKeyPath))
                {
                    key?.SetValue(userIdValueName, newUserId);
                }

                return newUserId;
            }
            catch (Exception ex)
            {
                // 如果注册表操作失败，使用默认用户名
                System.Diagnostics.Debug.WriteLine($"注册表操作失败: {ex.Message}");
                return "GUEST001";
            }
        }

        public UserCenterWindow()
        {
            InitializeComponent();

            // 初始化用户ID（第一次运行时生成，后续运行时从存储中读取）
            UserId = GetOrCreateUserId();

            var adduser = new
            {

            };

            var req= _httpClient.PostAsync("User/AddUser", new StringContent()).Result.Content.ReadAsStringAsync().Result;


            Products = new ObservableCollection<ProductItem>();
            ProductsListView.ItemsSource = Products;
            // 使用 Loaded 事件而不是构造函数来确保UI已准备好
            Loaded += async (s, e) =>
            {
                await InitializeStore();
                await LoadProductsAndCheckLicensesAsync();
                _storeContext.OfflineLicensesChanged += _storeContext_OfflineLicensesChanged;
            };
        }

        /// <summary>
        /// 当应用许可证的状态更改 (试用期已过期或用户已购买应用的完整版本) 时引发。
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="args"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void _storeContext_OfflineLicensesChanged(StoreContext sender, object args)
        {
            //当应用许可证的状态更改时，可以在这里处理相关逻辑
            MessageBox.Show("离线许可证状态已更改，请重新加载商品列表。", "提示",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 获取用户b2b令牌
        /// </summary>
        /// <returns></returns>
        private async Task<(string PurchaseId, string CollectionsId)> GetUserAccessTokens()
        {
            var req = await (await _httpClient.GetAsync("purchase/AccessTokens")).Content.ReadAsStringAsync();

            var res = JsonConvert.DeserializeObject<dynamic>(req)["AccessTokens"];
            string PurchaseId = res[0]["Token"];
            string CollectionsId = res[1]["Token"];

            return (PurchaseId, CollectionsId);
        }

        /// <summary>
        /// 初始化Store并关联窗口句柄
        /// </summary>
        private async Task InitializeStore()
        {
            try
            {
                _storeContext = StoreContext.GetDefault();
                //将窗口句柄与StoreContext关联
                var windowInteropHelper = new WindowInteropHelper(this);
                var hwnd = windowInteropHelper.Handle;
                var initWindow = (IInitializeWithWindow)(object)_storeContext;
                initWindow.Initialize(hwnd);

                // 显示用户名
                UserNameText.Text = $"用户：{UserId}";

                var tokens = await GetUserAccessTokens();
                _PurchaseId = tokens.PurchaseId;
                _CollectionsId = tokens.CollectionsId;

                //UserPurchaseId 有效期30天
                var getUserPurchaseId = await _storeContext.GetCustomerPurchaseIdAsync(tokens.PurchaseId, UserId);
                _CustomerPurchaseId = getUserPurchaseId;
                //UserCollectionsId
                //检索Microsoft应用商店 ID 密钥 有效期30天
                var getUserCollectionsId = await _storeContext.GetCustomerCollectionsIdAsync(tokens.CollectionsId, UserId);
                _CustomerCollectionsId = getUserCollectionsId;

                var user = new
                {
                    UserId = UserId,
                    UserPurchaseId = getUserPurchaseId,
                    UserCollectionsId = getUserCollectionsId
                };

                var requestContent = new StringContent(JsonConvert.SerializeObject(user), Encoding.UTF8, "application/json");

                //向服务端更新UserPurchaseId，UserCollectionsId
                _ = await (await _httpClient.PutAsync("User/UpdateUser", requestContent)).Content.ReadAsStringAsync();

                // 添加日志
                System.Diagnostics.Debug.WriteLine("Store 上下文初始化并关联窗口句柄完成");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化商店失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"初始化商店异常: {ex}");
            }
        }

        /// <summary>
        /// 从商店加载商品列表，并检查用户已拥有的许可证
        /// </summary>
        private async Task LoadProductsAndCheckLicensesAsync()
        {
            Products.Clear();

            try
            {
                System.Diagnostics.Debug.WriteLine("开始加载商品数据...");

                if (_storeContext == null)
                {
                    System.Diagnostics.Debug.WriteLine("StoreContext 为空，重新初始化");
                    await InitializeStore();
                }

                // 获取所有可购买商品
                //  当前支持以下值： Application、 Game、 Consumable、 UnmanagedConsumable 和 Durable。
                // Application 非游戏类应用，例如工具、效率类软件等。
                // Game 游戏类应用，例如休闲游戏、角色扮演游戏等。
                // Consumable 消耗品，例如游戏内货币、道具等。
                // UnmanagedConsumable 非托管消耗品，例如一次性使用的商品。
                // Durable 持久性商品，例如永久解锁的功能、扩展包等。
                var result = await _storeContext.GetAssociatedStoreProductsAsync(
                    new string[] { "Application", "Durable", "Consumable", "UnmanagedConsumable" });

                System.Diagnostics.Debug.WriteLine($"获取到商品数量: {result?.Products?.Count ?? 0}");

                if (result?.Products != null)
                {
                    foreach (var item in result.Products)
                    {
                        var product = item.Value;

                        var productItem = new ProductItem
                        {
                            StoreId = product.StoreId,
                            Title = product.Title,
                            Description = product.Description,
                            Price = product.Price.FormattedPrice,
                            ProductKind = product.ProductKind,
                            InAppOfferToken = product.InAppOfferToken,
                        };

                        productItem.PurchaseCommand = new RelayCommand(
                            () => PurchaseProduct(productItem),
                            // 允许购买消耗品、非托管消耗品，或尚未拥有的耐用品
                            () => productItem.ProductKind == "Consumable" || productItem.ProductKind == "UnmanagedConsumable" || !productItem.IsOwned
                        );
                        Products.Add(productItem);
                    }
                }
                // 加载完商品后，检查许可证状态
                await SyncLicensesAndBalancesAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载商品异常: {ex}");
                MessageBox.Show($"加载商品列表失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 同步所有商品的许可证和余额状态
        /// </summary>
        private async Task SyncLicensesAndBalancesAsync()
        {
            if (_storeContext == null) return;

            try
            {
                // 获取永久商品的许可证
                var appLicense = await _storeContext.GetAppLicenseAsync();

                var userLicense = appLicense.AddOnLicenses.Values;

                var ownedDurableIds = new HashSet<string>(
                    appLicense.AddOnLicenses.Values
                        .Where(l => l.IsActive)
                        .Select(l => l.SkuStoreId.Split('/')[0])
                );

                foreach (var product in Products)
                {
                    if (product.ProductKind == "Durable")
                    {
                        product.IsOwned = ownedDurableIds.Contains(product.StoreId);
                        if (product.IsOwned)
                        {
                            //UserNameText.Text = UserNameText.Text.Replace(UserId, "");
                            UserNameText.Text += $" {product.Title} ·";
                        }
                    }
                    else if (product.ProductKind == "Consumable")
                    {
                        // 获取消耗品的余额
                        StoreConsumableResult result = await _storeContext.GetConsumableBalanceRemainingAsync(product.StoreId);
                        if (result.Status == StoreConsumableStatus.Succeeded)
                        {
                            product.Balance = result.BalanceRemaining;
                        }
                    }
                    // UnmanagedConsumable 不需要同步状态，因为它们没有余额或永久许可证
                }
                System.Diagnostics.Debug.WriteLine("许可证和.balance状态同步完成。");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"同步状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 购买商品
        /// </summary>
        private async Task PurchaseProduct(ProductItem productToBuy)
        {
            if (_storeContext == null) return;

            try
            {
                System.Diagnostics.Debug.WriteLine($"开始购买商品 {productToBuy.StoreId}");
                var transactionId = Guid.NewGuid().ToString() + 6666;
                var purchaseProperties = new StorePurchaseProperties()
                {
                    Name = productToBuy.Title,
                    ExtendedJsonData = "{\"transactionId\":\"" + transactionId + "\",userId:\"" + this.UserId + "\"}", //测试而已，目前没有任何作用
                };

                var orderParm = new
                {
                    Name = productToBuy.Title,
                    OrderId = Guid.NewGuid().ToString(),
                    Price = double.Parse(productToBuy.Price.Replace("¥", "").Trim()),
                    UserId = this.UserId,
                    StoreId = productToBuy.StoreId,
                    ProductKind = productToBuy.ProductKind,
                    InAppOfferToken = productToBuy.InAppOfferToken,
                };

                string json = JsonConvert.SerializeObject(orderParm);
                HttpContent httpContent = new StringContent(json, Encoding.UTF8, "application/json");

                var orderResult = await (await _httpClient.PostAsync("Order/AddOrder", httpContent)).Content.ReadAsStringAsync();

                var jsonResult = JsonConvert.DeserializeObject<dynamic>(orderResult);

                if ((bool)jsonResult["result"] != true)
                {
                    MessageBox.Show("创建订单失败！。", "错误");
                    return;
                }

                StorePurchaseResult result = await _storeContext.RequestPurchaseAsync(productToBuy.StoreId,new StorePurchaseProperties(productToBuy.Title));

                switch (result.Status)
                {
                    case StorePurchaseStatus.Succeeded:
                        try
                        {
                            // 显示等待界面
                            LoadingOverlay.Visibility = Visibility.Visible;
                            LoadingText.Text = "正在验证购买，请稍候...";

                            // 向服务端验证订单（轮询方式）
                            bool orderVerified = false;
                            int maxAttempts = 10; // 最大尝试次数（10次 * 3秒 = 30秒）
                            int currentAttempt = 0;
                            dynamic verifyJson = null;

                            while (!orderVerified && currentAttempt < maxAttempts)
                            {
                                // 更新加载文本显示当前进度
                                LoadingText.Text = $"正在验证购买，请稍候...({currentAttempt + 1}/{maxAttempts})";
                                
                                // 向服务端验证订单
                                var verifyResult = await (await _httpClient.GetAsync($"Order/VerifyOrder?orderId={jsonResult["orderId"]}")).Content.ReadAsStringAsync();
                                verifyJson = JsonConvert.DeserializeObject<dynamic>(verifyResult);
                                
                                // 检查是否验证成功
                                if (verifyJson != null && (bool)verifyJson["result"] == true)
                                {
                                    orderVerified = true;
                                    System.Diagnostics.Debug.WriteLine($"订单验证成功，尝试次数：{currentAttempt + 1}");
                                    break;
                                }
                                
                                // 未成功，等待3秒后重试
                                currentAttempt++;
                                if (!orderVerified && currentAttempt < maxAttempts)
                                {
                                    System.Diagnostics.Debug.WriteLine($"订单验证未成功，3秒后进行第{currentAttempt + 1}次尝试");
                                    await Task.Delay(3000); // 等待3秒
                                }
                            }

                            // 购买成功后，需要验证许可证才能确认交易
                            bool success = await SyncSingleProductStatusAsync(productToBuy, true);

                            // 隐藏等待界面
                            LoadingOverlay.Visibility = Visibility.Collapsed;

                            if (success && orderVerified)
                            {
                                //这里可以添加授予用户物品或功能的代码
                                MessageBox.Show("购买成功！", "提示");
                            }
                            else
                            {
                                if (!orderVerified)
                                {
                                    MessageBox.Show($"订单验证失败，已尝试{currentAttempt}次，请稍后重试。", "错误");
                                }
                                else
                                {
                                    MessageBox.Show("购买验证失败，请稍后重试。", "错误");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            // 出错时也要隐藏等待界面
                            LoadingOverlay.Visibility = Visibility.Collapsed;
                            MessageBox.Show($"验证购买时出错: {ex.Message}", "错误");
                        }
                        break;

                    case StorePurchaseStatus.AlreadyPurchased:
                        MessageBox.Show("您已经购买过此商品", "提示");
                        // 同步状态
                        await SyncSingleProductStatusAsync(productToBuy, false);
                        break;

                    case StorePurchaseStatus.NotPurchased:
                        MessageBox.Show("购买已取消", "提示");
                        break;

                    case StorePurchaseStatus.NetworkError:
                        MessageBox.Show("网络错误，请重试", "错误");
                        break;

                    case StorePurchaseStatus.ServerError:
                        MessageBox.Show("服务器错误，请稍后再试", "错误");
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"购买过程中出错: {ex}");
                MessageBox.Show($"购买过程中出错: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 刷新单个商品的状态，或履行一个非托管消耗品
        /// </summary>
        private async Task<bool> SyncSingleProductStatusAsync(ProductItem product, bool fulfill)
        {
            if (product.ProductKind == "Durable")
            {
                // 验证许可证
                var appLicense = await _storeContext.GetAppLicenseAsync();
                var ownedDurableIds = new HashSet<string>(
                    appLicense.AddOnLicenses.Values
                        .Where(l => l.IsActive)
                        .Select(l => l.SkuStoreId.Split('/')[0])
                );
                product.IsOwned = ownedDurableIds.Contains(product.StoreId);
                return product.IsOwned;
            }
            else if (product.ProductKind == "Consumable")
            {
                StoreConsumableResult result = await _storeContext.GetConsumableBalanceRemainingAsync(product.StoreId);
                if (result.Status == StoreConsumableStatus.Succeeded)
                {
                    product.Balance = result.BalanceRemaining;
                    return true;
                }
                return false;
            }
            else if (product.ProductKind == "UnmanagedConsumable" && fulfill)
            {
                // 履行非托管消耗品，这是完成交易的必要步骤
                Guid trackingId = Guid.NewGuid();
                StoreConsumableResult result = await _storeContext.ReportConsumableFulfillmentAsync(
                    product.StoreId, 1, trackingId);

                if (result.Status == StoreConsumableStatus.Succeeded)
                {
                    System.Diagnostics.Debug.WriteLine($"非托管消耗品 {product.StoreId} 已成功履行。");
                    // 在这里添加授予用户物品或功能的代码
                    return true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"非托管消耗品 {product.StoreId} 履行失败: {result.Status}");
                    return false;
                }
            }
            return true; // 对于其他情况或非履行，我们假设是成功的
        }

        /// <summary>
        ///恢复购买按钮的点击事件处理
        /// </summary>
        private async void RestorePurchases_Click(object sender, RoutedEventArgs e)
        {
            UserNameText.Text = $"用户：{UserId}";
            // 重新检查所有商品的许可证和余额
            await SyncLicensesAndBalancesAsync();
            MessageBox.Show("购买已恢复。", "提示");
        }
    }

    /// <summary>
    /// 商品信息类，扩展以支持不同类型的商品
    /// </summary>
    public class ProductItem : INotifyPropertyChanged
    {
        private bool _isOwned;
        private uint _balance;

        public string StoreId { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string Price { get; set; }
        public string ProductKind { get; set; }

        /// <summary>
        /// 加载项 代码名称
        /// </summary>
        public string InAppOfferToken { get; set; }

        public ICommand PurchaseCommand { get; set; }

        public bool IsOwned
        {
            get => _isOwned;
            set
            {
                if (_isOwned != value)
                {
                    _isOwned = value;
                    OnPropertyChanged(nameof(IsOwned));
                    OnPropertyChanged(nameof(StatusText)); // 状态文本依赖于此
                }
            }
        }

        public uint Balance
        {
            get => _balance;
            set
            {
                if (_balance != value)
                {
                    _balance = value;
                    OnPropertyChanged(nameof(Balance));
                    OnPropertyChanged(nameof(StatusText)); // 状态文本依赖于此
                }
            }
        }

        /// <summary>
        /// 用于在UI上显示的动态状态文本
        /// </summary>
        public string StatusText
        {
            get
            {
                if (ProductKind == "Durable")
                {
                    return IsOwned ? "已拥有" : "永久";
                }
                if (ProductKind == "Consumable")
                {
                    return $"剩余: {Balance}";
                }
                if (ProductKind == "UnmanagedConsumable")
                {
                    return "一次性购买";
                }
                return string.Empty;
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 命令类，增加了对CanExecute的判断
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Func<Task> _execute;
        private readonly Func<bool> _canExecute;

        public RelayCommand(Func<Task> execute, Func<bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        // 当CanExecute的条件变化时，此事件会通知UI（如按钮的IsEnabled状态）
        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object parameter) => _canExecute == null || _canExecute();

        public async void Execute(object parameter)
        {
            await _execute();
        }
    }

    /// <summary>
    /// 定义 IInitializeWithWindow COM 接口
    /// </summary>
    [ComImport]
    [Guid("3E68D4BD-7135-4D10-8018-9FB6D9F33FA1")]
    [InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    public interface IInitializeWithWindow
    {
        void Initialize(IntPtr hwnd);
    }
}