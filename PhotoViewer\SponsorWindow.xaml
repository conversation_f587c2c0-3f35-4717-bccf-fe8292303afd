﻿<Window x:Class="PhotoViewer.SponsorWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="赞助" Height="300" Width="400"
        ShowInTaskbar="False"
        WindowStartupLocation="CenterOwner">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto">
            </RowDefinition>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <Image Grid.Row="0" Name="SponsorImage" Width="64" Height="64" Stretch="Fill"></Image>
        <TextBlock Grid.Row="1" Name="textBlock"></TextBlock>
        <ListBox Grid.Row="2" Name="AddOnsListBox" />
    </Grid>
</Window>