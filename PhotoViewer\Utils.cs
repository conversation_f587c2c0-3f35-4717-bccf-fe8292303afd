﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Management; // 需要添加System.Management NuGet包
using System.Security.Cryptography;
using System.IO;

namespace PhotoViewer
{
    public class Utils
    {
        public string GenerateDeviceId()
        {
            // 获取多个硬件标识组合
            var cpuId = GetHardwareInfo("Win32_Processor", "ProcessorId");
            var biosId = GetHardwareInfo("Win32_BIOS", "SerialNumber");
            var baseId = GetHardwareInfo("Win32_BaseBoard", "SerialNumber");

            // 组合并哈希生成设备ID
            var combined = $"{cpuId}|{biosId}|{baseId}";
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                byte[] hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(combined));
                return BitConverter.ToString(hash).Replace("-", "").Substring(0, 16);
            }
        }

        private string GetHardwareInfo(string className, string propertyName)
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher($"SELECT {propertyName} FROM {className}"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        return obj[propertyName]?.ToString() ?? string.Empty;
                    }
                }
            }
            catch { return string.Empty; }
            return string.Empty;
        }
    }
}
