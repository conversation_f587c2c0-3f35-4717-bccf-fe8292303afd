﻿using Microsoft.Win32;
using PhotoViewer.SettingWindow;
using PhotoViewer.Store;
using PhotoViewer.UserCenter;
using System;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Threading;

namespace PhotoViewer
{
    public partial class MainWindow : Window
    {
        private double _currentScale = 1.0;
        private const double ZoomFactor = 1.1; // 减小缩放因子使缩放更平滑
        private bool _isFitToWindow = false;
        private bool _isFullscreen = false;
        private WindowState _previousWindowState;
        private DispatcherTimer _zoomSnapTimer;
        private bool _isZoomSnapping = false;

        private bool _isDragging = false;
        private Point _dragStartPoint;
        private double _originalScale = 1.0;

        public MainWindow()
        {
            InitializeComponent();
            this.PreviewKeyDown += MainWindow_PreviewKeyDown;

            if (AdminCheckHelper.IsRunAsAdministrator())
            {
                MessageBox.Show("程序正在以管理员身份运行，将尝试以普通权限重新启动以启用支付功能。", "提示");
                AdminCheckHelper.RelaunchAsStandardUser();
                return;
            }

            // 初始化缩放迟滞计时器
            _zoomSnapTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(250)
            };
            _zoomSnapTimer.Tick += ZoomSnapTimer_Tick;

            ImageViewer.MouseLeftButtonDown += ImageViewer_MouseLeftButtonDown;
            ImageViewer.MouseLeftButtonUp += ImageViewer_MouseLeftButtonUp;


        }

        private void OpenButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "图片文件|*.jpg;*.jpeg;*.png;*.bmp;*.gif|所有文件|*.*",
                Title = "选择一张图片"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                LoadImage(openFileDialog.FileName);
            }
        }

        private void CloseImage_Click(object sender, RoutedEventArgs e)
        {
            // 清除当前动图（如果存在）和图片资源
            WpfAnimatedGif.ImageBehavior.SetAnimatedSource(ImageViewer, null);
            ImageViewer.Source = null;
            _currentScale = 1.0;
            _isFitToWindow = false;
            UpdateZoomText();
            CursorPositionText.Text = "";
        }

        private void Exit_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        public void LoadImage(string filePath)
        {
            try
            {
                // 清除当前图片
                WpfAnimatedGif.ImageBehavior.SetAnimatedSource(ImageViewer, null);
                ImageViewer.Source = null;

                // 创建 BitmapImage 对象加载图片
                var bitmap = new System.Windows.Media.Imaging.BitmapImage();
                bitmap.BeginInit();
                bitmap.UriSource = new System.Uri(filePath);
                bitmap.CacheOption = System.Windows.Media.Imaging.BitmapCacheOption.OnLoad;
                bitmap.EndInit();

                this.Title = System.IO.Path.GetFileName(filePath);

                // 计算“原始尺寸”对应的比例：实际显示 1:1 需要乘上的因子（基于 DPI）
                // 方法一：根据 PixelWidth 与 Width 的比值
                //_originalScale = bitmap.PixelWidth / bitmap.Width;
                // 或者方法二：直接依据 DPI（推荐）
                _originalScale = bitmap.DpiX < 96 ? 1.0 : bitmap.DpiX / 96.0;

                // 以下根据文件类型选择是否播放动图
                if (System.IO.Path.GetExtension(filePath).ToLower() == ".gif")
                {
                    WpfAnimatedGif.ImageBehavior.SetAnimatedSource(ImageViewer, bitmap);
                }
                else
                {
                    ImageViewer.Source = bitmap;
                }

                // 延迟执行判断，确保 ScrollViewer 的尺寸已更新
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    double imageActualWidth = bitmap.Width * _originalScale;
                    double imageActualHeight = bitmap.Height * _originalScale;

                    var availableWidth = ScrollViewer.ActualWidth - SystemParameters.VerticalScrollBarWidth;
                    var availableHeight = ScrollViewer.ActualHeight - SystemParameters.HorizontalScrollBarHeight;

                    if (imageActualWidth > availableWidth || imageActualHeight > availableHeight)
                    {
                        _isFitToWindow = true;
                        FitImageToWindow();
                    }
                    else
                    {
                        _isFitToWindow = false;
                        _currentScale = _originalScale;
                        CenterImage();
                    }

                    UpdateZoomText();
                }), System.Windows.Threading.DispatcherPriority.Loaded);
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"无法加载图片: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ZoomInButton_Click(object sender, RoutedEventArgs e)
        {
            if (ImageViewer.Source != null)
            {
                ImageViewer.Stretch = Stretch.None;
                // 获取当前窗口的中心点
                var centerPoint = new Point(ScrollViewer.ViewportWidth / 2, ScrollViewer.ViewportHeight / 2);
                ZoomImage(ZoomFactor, centerPoint);
            }
        }

        private void ZoomOutButton_Click(object sender, RoutedEventArgs e)
        {
            if (ImageViewer.Source != null && _currentScale / ZoomFactor > 0.1)
            {
                ImageViewer.Stretch = Stretch.None;
                // 获取当前窗口的中心点
                var centerPoint = new Point(ScrollViewer.ViewportWidth / 2, ScrollViewer.ViewportHeight / 2);
                ZoomImage(1 / ZoomFactor, centerPoint);
            }
        }

        private void OriginalSizeButton_Click(object sender, RoutedEventArgs e)
        {
            if (ImageViewer.Source != null)
            {
                // 保存旧的缩放比
                double oldScale = _currentScale;
                ImageViewer.Stretch = Stretch.None;

                // 计算当前 ScrollViewer 的可见区域中心点（该坐标是相对于滚动内容而言）
                double viewportCenterX = ScrollViewer.ViewportWidth / 2;
                double viewportCenterY = ScrollViewer.ViewportHeight / 2;
                // 当前滚动偏移下的内容中心点
                double currentContentCenterX = ScrollViewer.HorizontalOffset + viewportCenterX;
                double currentContentCenterY = ScrollViewer.VerticalOffset + viewportCenterY;

                // 计算该中心点在未缩放图片（逻辑坐标）中的位置
                double unscaledCenterX = currentContentCenterX / oldScale;
                double unscaledCenterY = currentContentCenterY / oldScale;

                // 切换到原始（1:1像素）显示，即把 _currentScale 设置为 _originalScale
                _currentScale = _originalScale;
                ImageViewer.LayoutTransform = new ScaleTransform(_currentScale, _currentScale);
                // 更新布局后才能获取最新 ScrollViewer.Extent
                ScrollViewer.UpdateLayout();

                // 计算在新缩放下，该未缩放中心点对应的坐标（即它在新的缩放状态下的位置）
                double newContentCenterX = unscaledCenterX * _currentScale;
                double newContentCenterY = unscaledCenterY * _currentScale;

                // 计算新的滚动偏移，保证 newContentCenter 出现在视口中心
                double newHorizontalOffset = newContentCenterX - viewportCenterX;
                double newVerticalOffset = newContentCenterY - viewportCenterY;

                ScrollViewer.ScrollToHorizontalOffset(newHorizontalOffset);
                ScrollViewer.ScrollToVerticalOffset(newVerticalOffset);

                UpdateZoomText();
            }
        }

        private void FitToWindowButton_Click(object sender, RoutedEventArgs e)
        {
            if (ImageViewer.Source != null)
            {
                _isFitToWindow = true;
                FitImageToWindow();
                UpdateZoomText();
            }
        }

        private void ToggleToolbar_Click(object sender, RoutedEventArgs e)
        {
            MainToolbar.Visibility = MainToolbar.Visibility == Visibility.Visible ?
                Visibility.Collapsed : Visibility.Visible;

            ToggleToolbarMenuItem.Header = MainToolbar.Visibility == Visibility.Visible ?
                "隐藏工具栏" : "显示工具栏";
        }

        private void ToggleStatusbar_Click(object sender, RoutedEventArgs e)
        {
            MainStatusBar.Visibility = MainStatusBar.Visibility == Visibility.Visible ?
                Visibility.Collapsed : Visibility.Visible;

            ToggleStatusbarMenuItem.Header = MainStatusBar.Visibility == Visibility.Visible ?
                "隐藏状态栏" : "显示状态栏";
        }

        private void ToggleFullscreen_Click(object sender, RoutedEventArgs e)
        {
            if (_isFullscreen)
            {
                WindowState = _previousWindowState;
                WindowStyle = WindowStyle.SingleBorderWindow;
                _isFullscreen = false;
            }
            else
            {
                _previousWindowState = WindowState;
                WindowState = WindowState.Maximized;
                WindowStyle = WindowStyle.None;
                _isFullscreen = true;
            }
        }

        private void About_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("照片查看器\n版本 1.0\n开发者: iRedwhale", "关于",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void MenuStore_Click(object sender, RoutedEventArgs e)
        {
            var productInfoWindow = new StoreFunListWindow
            {
                Owner = this
            };
            productInfoWindow.ShowDialog();
        }

        private void Sponsor_Click(object sender, RoutedEventArgs e)
        {
            var sponsorWindow = new SponsorWindow
            {
                Owner = this
            };
            sponsorWindow.ShowDialog();
        }

        private void MenuFormatAssociation_Click(object sender, RoutedEventArgs e)
        {
            FormatAssociationWindow faWindow = new FormatAssociationWindow();
            // 设置当前窗口为 owner，使对话框居中显示
            faWindow.Owner = this;
            faWindow.ShowDialog();
        }

        private void Window_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (_isFitToWindow && ImageViewer.Source != null)
            {
                FitImageToWindow();
            }
        }

        private void ScrollViewer_PreviewMouseWheel(object sender, MouseWheelEventArgs e)
        {
            if (ImageViewer.Source != null)
            {
                ImageViewer.Stretch = Stretch.None;
                // 获取鼠标相对于 ScrollViewer 的位置
                var mousePosition = e.GetPosition(ScrollViewer);

                double zoomFactor = e.Delta > 0 ? ZoomFactor : 1 / ZoomFactor;
                ZoomImage(zoomFactor, mousePosition);
                e.Handled = true;
            }
        }

        private void ImageViewer_MouseMove(object sender, MouseEventArgs e)
        {
            if (ImageViewer.Source != null)
            {
                if (_isDragging)
                {
                    // 获取当前鼠标在 ScrollViewer 中的位置
                    Point currentPosition = e.GetPosition(ScrollViewer);
                    // 计算移动的偏移量
                    double deltaX = currentPosition.X - _dragStartPoint.X;
                    double deltaY = currentPosition.Y - _dragStartPoint.Y;
                    // 更新 ScrollViewer 的偏移，使图片跟随鼠标移动
                    ScrollViewer.ScrollToHorizontalOffset(ScrollViewer.HorizontalOffset - deltaX);
                    ScrollViewer.ScrollToVerticalOffset(ScrollViewer.VerticalOffset - deltaY);
                    // 更新起始位置，便于连续拖动
                    _dragStartPoint = currentPosition;
                }

                // 继续显示鼠标在图片上的坐标（原有功能）
                var position = e.GetPosition(ImageViewer);
                CursorPositionText.Text = $"X: {(int)position.X}, Y: {(int)position.Y}";
            }
        }

        private void ZoomImage(double zoomFactor, Point mousePos)
        {
            if (ImageViewer.Source == null) return;

            _isFitToWindow = false;
            double oldScale = _currentScale;
            double newScale = _currentScale * zoomFactor;
            newScale = Math.Max(0.1, Math.Min(newScale, 20.0));

            // 当新缩放值接近 “原始尺寸”时进行吸附，允许一定的误差范围
            if (Math.Abs(newScale - _originalScale) < 0.05 * _originalScale && !_isZoomSnapping)
            {
                _isZoomSnapping = true;
                _zoomSnapTimer.Start();
                newScale = _originalScale;
            }

            // 保存当前滚动位置
            double oldHorizontalOffset = ScrollViewer.HorizontalOffset;
            double oldVerticalOffset = ScrollViewer.VerticalOffset;

            // 更新缩放比例并应用变换
            _currentScale = newScale;
            ImageViewer.LayoutTransform = new ScaleTransform(_currentScale, _currentScale);

            // 根据鼠标位置和滚动偏移计算新的滚动位置
            double newHorizontalOffset = (oldHorizontalOffset + mousePos.X) * zoomFactor - mousePos.X;
            double newVerticalOffset = (oldVerticalOffset + mousePos.Y) * zoomFactor - mousePos.Y;

            ScrollViewer.ScrollToHorizontalOffset(newHorizontalOffset);
            ScrollViewer.ScrollToVerticalOffset(newVerticalOffset);

            UpdateZoomText();
        }

        private void ZoomSnapTimer_Tick(object sender, EventArgs e)
        {
            _isZoomSnapping = false;
            _zoomSnapTimer.Stop();
        }

        private void FitImageToWindow()
        {
            if (ImageViewer.Source == null) return;

            // 以下计算逻辑参考之前的代码
            var imageLogicWidth = ImageViewer.Source.Width;
            var imageLogicHeight = ImageViewer.Source.Height;
            var imageActualWidth = imageLogicWidth * _originalScale;
            var imageActualHeight = imageLogicHeight * _originalScale;

            var availableWidth = ScrollViewer.ActualWidth - SystemParameters.VerticalScrollBarWidth;
            var availableHeight = ScrollViewer.ActualHeight - SystemParameters.HorizontalScrollBarHeight;

            var scaleX = availableWidth / imageActualWidth;
            var scaleY = availableHeight / imageActualHeight;

            _currentScale = _originalScale * Math.Min(scaleX, scaleY);

            // 当匹配窗口时，临时改变 Stretch 使图片铺满可见区域
            ImageViewer.Stretch = Stretch.Uniform;
            var centerPoint = new Point(ScrollViewer.ViewportWidth / 2, ScrollViewer.ViewportHeight / 2);
            UpdateImageScale(centerPoint);
        }

        private void UpdateZoomText()
        {
            // 显示的缩放率为当前缩放相对于原始尺寸的比例
            double percentage = (_currentScale / _originalScale) * 100;
            ZoomLevelText.Text = $"缩放: {percentage:0}%";
        }

        private void CenterImage()
        {
            if (ImageViewer.Source == null) return;

            ImageViewer.LayoutTransform = new ScaleTransform(_currentScale, _currentScale);

            // 计算居中位置
            double horizontalOffset = (ScrollViewer.ExtentWidth - ScrollViewer.ViewportWidth) / 2;
            double verticalOffset = (ScrollViewer.ExtentHeight - ScrollViewer.ViewportHeight) / 2;

            ScrollViewer.ScrollToHorizontalOffset(Math.Max(0, horizontalOffset));
            ScrollViewer.ScrollToVerticalOffset(Math.Max(0, verticalOffset));
        }

        private void UpdateImageScale(Point zoomCenter)
        {
            if (ImageViewer.Source == null)
                return;

            // 先应用新的缩放变换
            ImageViewer.LayoutTransform = new ScaleTransform(_currentScale, _currentScale);

            // 强制更新布局，确保 ScrollViewer 的 ExtentWidth/ExtentHeight 已刷新
            ScrollViewer.UpdateLayout();

            if (ScrollViewer.ExtentWidth == 0 || ScrollViewer.ExtentHeight == 0)
                return;

            // 计算 zoomCenter 在整个滚动内容中的相对位置
            double relativeX = (zoomCenter.X + ScrollViewer.HorizontalOffset) / ScrollViewer.ExtentWidth;
            double relativeY = (zoomCenter.Y + ScrollViewer.VerticalOffset) / ScrollViewer.ExtentHeight;

            // 根据比例计算新的滚动偏移，以保持 zoomCenter 的位置不变
            double newHorizontalOffset = relativeX * ScrollViewer.ExtentWidth - zoomCenter.X;
            double newVerticalOffset = relativeY * ScrollViewer.ExtentHeight - zoomCenter.Y;

            ScrollViewer.ScrollToHorizontalOffset(newHorizontalOffset);
            ScrollViewer.ScrollToVerticalOffset(newVerticalOffset);
        }

        private void ImageViewer_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (ImageViewer.Source != null)
            {
                _isDragging = true;
                // 记录鼠标在 ScrollViewer 中的起始位置
                _dragStartPoint = e.GetPosition(ScrollViewer);
                ImageViewer.CaptureMouse();
            }
        }

        private void ImageViewer_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (_isDragging)
            {
                _isDragging = false;
                ImageViewer.ReleaseMouseCapture();
            }
        }

        private Key _lastKey;
        private DateTime _lastKeyTime;
        private readonly TimeSpan _comboTimeout = TimeSpan.FromSeconds(2);

        private void MainWindow_PreviewKeyDown(object sender, KeyEventArgs e)
        {
            if ((Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control && e.Key == Key.T)
            {
                if (_lastKey == Key.T && (DateTime.Now - _lastKeyTime) < _comboTimeout)
                {
                    OpenSecretWindow();
                    _lastKey = Key.None;
                }
                else
                {
                    _lastKey = Key.T;
                    _lastKeyTime = DateTime.Now;
                }

                e.Handled = true;
            }
            else
            {
                _lastKey = Key.None;
            }
        }

        private void OpenSecretWindow()
        {
            var storeFunListWindow = new StoreFunListWindow(); // 替换成你要打开的窗口类
            storeFunListWindow.Owner = this;
            storeFunListWindow.ShowDialog();
        }

        private void Person_Click(object sender, RoutedEventArgs e)
        {
            UserCenterWindow userCenter = new UserCenterWindow();
            userCenter.Owner = this;
            userCenter.ShowDialog();
        }
    }
}