﻿<Window x:Class="PhotoViewer.UserCenter.UserCenterWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:PhotoViewer.UserCenter"
        mc:Ignorable="d"
        WindowStartupLocation="CenterScreen"
        Title="用户中心" Height="450" Width="800">
    <Window.Resources>
        <!-- 添加LoadingOverlay样式 -->
        <Style x:Key="LoadingOverlayStyle" TargetType="Grid">
            <Setter Property="Background" Value="#80000000"/>
            <Setter Property="Visibility" Value="Collapsed"/>
        </Style>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <StackPanel Grid.Row="0" Margin="10">
            <TextBlock x:Name="UserNameText" FontSize="18" FontWeight="Bold" Text="用户：游客"/>
        </StackPanel>

        <ListView x:Name="ProductsListView" Grid.Row="1" Margin="10" ItemsSource="{Binding Products}">
            <ListView.ItemTemplate>
                <DataTemplate DataType="{x:Type local:ProductItem}">
                    <Grid Margin="5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="{Binding Title}" FontSize="16" FontWeight="Bold"/>
                            <TextBlock Text="{Binding Description}" TextWrapping="Wrap" Margin="0,5,0,0"/>
                            <TextBlock Text="{Binding Price}" Foreground="DarkGreen" Margin="0,5,0,0"/>
                            <TextBlock Text="{Binding StatusText}" Foreground="Blue" FontWeight="SemiBold" Margin="0,5,0,0"/>
                        </StackPanel>
                        
                        <!-- 关键更新：购买按钮的状态和文本会根据是否已购买自动变化 -->
                        <Button Grid.Column="1" Command="{Binding PurchaseCommand}" VerticalAlignment="Center" Padding="10,5" MinWidth="80">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Content" Value="购买"/>
                                    <Style.Triggers>
                                        <!-- 使用 MultiDataTrigger 替代 DataTrigger 来检查多个条件 -->
                                        <MultiDataTrigger>
                                            <MultiDataTrigger.Conditions>
                                                <Condition Binding="{Binding IsOwned}" Value="True"/>
                                                <Condition Binding="{Binding ProductKind}" Value="Durable"/>
                                            </MultiDataTrigger.Conditions>
                                            <Setter Property="Content" Value="已拥有"/>
                                        </MultiDataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                        </Button>
                    </Grid>
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>

        <Button Grid.Row="2" Content="恢复购买" Click="RestorePurchases_Click" 
                HorizontalAlignment="Right" Margin="10" Padding="10,5"/>
        
        <!-- 等待界面 -->
        <Grid x:Name="LoadingOverlay" Grid.RowSpan="3" Style="{StaticResource LoadingOverlayStyle}" Panel.ZIndex="1000">
            <Border Background="White" CornerRadius="8" Width="300" Height="150" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <ProgressBar Grid.Row="0" IsIndeterminate="True" Width="200" Height="10" Margin="0,20,0,10"/>
                    <TextBlock Grid.Row="1" x:Name="LoadingText" Text="正在验证购买，请稍候..." HorizontalAlignment="Center" Margin="0,10" FontSize="14"/>
                    <TextBlock Grid.Row="2" Text="请勿关闭此窗口" HorizontalAlignment="Center" Margin="0,5,0,20" FontSize="12" Foreground="#666666"/>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Window>