<mxfile host="drawio.min2k.com" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="@DRAWIO-VERSION@">
  <diagram name="第 1 页" id="pz6rZi3kaJAcaQjHZxC9">
    <mxGraphModel dx="2952" dy="1172" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="v5o1fB2vA18KuS8kNP7B-1" value="&lt;span style=&quot;color: rgba(51, 54, 57, 0.9); font-family: -apple-system, BlinkMacSystemFont, &amp;quot;Segoe UI&amp;quot;, Roboto, &amp;quot;Helvetica Neue&amp;quot;, Arial, &amp;quot;Noto Sans&amp;quot;, sans-serif, &amp;quot;Apple Color Emoji&amp;quot;, &amp;quot;Segoe UI Emoji&amp;quot;; font-size: 14px; text-align: left; white-space-collapse: preserve; background-color: rgb(255, 255, 255);&quot;&gt;/purchase/AccessTokens&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="210" y="80" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-2" value="获得访问令牌&lt;div&gt;&lt;br/&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="280" y="50" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-13" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="v5o1fB2vA18KuS8kNP7B-9" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="100" y="500" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-14" value="&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-family: monospace; font-size: 0px; text-align: start; background-color: rgb(251, 251, 251);&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%26lt%3Bspan%20style%3D%26quot%3Bcolor%3A%20rgb(0%2C%20104%2C%20129)%3B%20font-family%3A%20SFMono-Regular%2C%20Consolas%2C%20%26amp%3Bquot%3BLiberation%20Mono%26amp%3Bquot%3B%2C%20Menlo%2C%20Courier%2C%20monospace%3B%20font-size%3A%2014px%3B%20text-align%3A%20start%3B%20white-space-collapse%3A%20preserve%3B%20background-color%3A%20rgb(240%2C%20240%2C%20240)%3B%26quot%3B%26gt%3BGetAssociatedStoreProductsAsync%26lt%3B%2Fspan%26gt%3B%22%20style%3D%22rounded%3D0%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3B%22%20vertex%3D%221%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%2240%22%20y%3D%22460%22%20width%3D%22120%22%20height%3D%2230%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="v5o1fB2vA18KuS8kNP7B-13" vertex="1" connectable="0">
          <mxGeometry x="-0.0481" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-15" value="&lt;span style=&quot;color: rgb(0, 104, 129); font-family: SFMono-Regular, Consolas, &amp;quot;Liberation Mono&amp;quot;, Menlo, Courier, monospace; font-size: 14px; text-align: start; white-space: pre-wrap; background-color: rgb(240, 240, 240);&quot;&gt;GetAssociatedStoreProductsAsync&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="v5o1fB2vA18KuS8kNP7B-13" vertex="1" connectable="0">
          <mxGeometry x="0.0139" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" source="v5o1fB2vA18KuS8kNP7B-9" target="v5o1fB2vA18KuS8kNP7B-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-7" value="&lt;span style=&quot;font-size: 14px;&quot;&gt;1.请求使用&lt;/span&gt;&lt;span style=&quot;font-size: 14px; color: rgba(51, 54, 57, 0.9); font-family: -apple-system, BlinkMacSystemFont, &amp;quot;Segoe UI&amp;quot;, Roboto, &amp;quot;Helvetica Neue&amp;quot;, Arial, &amp;quot;Noto Sans&amp;quot;, sans-serif, &amp;quot;Apple Color Emoji&amp;quot;, &amp;quot;Segoe UI Emoji&amp;quot;; white-space: pre-wrap;&quot;&gt;Authorization:&lt;/span&gt;&lt;span style=&quot;font-size: 14px; color: rgba(51, 54, 57, 0.9); font-family: -apple-system, BlinkMacSystemFont, &amp;quot;Segoe UI&amp;quot;, Roboto, &amp;quot;Helvetica Neue&amp;quot;, Arial, &amp;quot;Noto Sans&amp;quot;, sans-serif, &amp;quot;Apple Color Emoji&amp;quot;, &amp;quot;Segoe UI Emoji&amp;quot;; white-space: pre-wrap;&quot;&gt;test666(唯一用户标识)&lt;br&gt;&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="v5o1fB2vA18KuS8kNP7B-25">
          <mxGeometry x="0.5612" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-31" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="v5o1fB2vA18KuS8kNP7B-30" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="50" y="510.36" as="sourcePoint" />
            <mxPoint x="-100" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-32" value="3.客户端发起支付" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="v5o1fB2vA18KuS8kNP7B-31" vertex="1" connectable="0">
          <mxGeometry x="0.0577" y="4" relative="1" as="geometry">
            <mxPoint x="54" y="-29" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="v5o1fB2vA18KuS8kNP7B-9" target="v5o1fB2vA18KuS8kNP7B-22">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-8" value="&lt;span style=&quot;font-size: 14px; color: rgba(51, 54, 57, 0.9); font-family: -apple-system, BlinkMacSystemFont, &amp;quot;Segoe UI&amp;quot;, Roboto, &amp;quot;Helvetica Neue&amp;quot;, Arial, &amp;quot;Noto Sans&amp;quot;, sans-serif, &amp;quot;Apple Color Emoji&amp;quot;, &amp;quot;Segoe UI Emoji&amp;quot;; white-space: pre-wrap;&quot;&gt;2.客户端将获得的&lt;/span&gt;&lt;span style=&quot;font-size: 14px; background-color: rgb(251, 251, 251);&quot;&gt;purchaseToken,&lt;/span&gt;&lt;span style=&quot;font-size: 14px; background-color: rgb(251, 251, 251);&quot;&gt;collectionsToken使用&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;GetCustomerPurchaseIdAsync,GetCustomerCollectionsIdAsync&lt;br&gt;把Token变成b2b令牌，并把获得的令牌交予服务端维护&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="8X5j39H2abwFqZzt46MZ-5">
          <mxGeometry x="0.3186" y="-1" relative="1" as="geometry">
            <mxPoint x="126" y="34" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-9" value="客户端" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="40" y="390" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-18" value="&lt;span style=&quot;color: rgb(22, 22, 22); font-family: &amp;quot;Segoe UI&amp;quot;, &amp;quot;Segoe UI Variable Text&amp;quot;, -apple-system, BlinkMacSystemFont, &amp;quot;Helvetica Neue&amp;quot;, Helvetica, Arial, sans-serif; font-size: 16px; text-align: start; background-color: rgb(255, 255, 255);&quot;&gt;获取用户可购买的产品&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="20" y="500" width="170" height="30" as="geometry" />
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-41" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.485;entryY=0.111;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="v5o1fB2vA18KuS8kNP7B-22" target="v5o1fB2vA18KuS8kNP7B-42" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-43" value="4.将需要验证的订单放到队列里面，提供一个前台问询函数" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="v5o1fB2vA18KuS8kNP7B-41" vertex="1" connectable="0">
          <mxGeometry x="0.5635" y="1" relative="1" as="geometry">
            <mxPoint x="11" y="24" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-22" value="服务端" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="390" y="310" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.509;entryY=0.175;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="v5o1fB2vA18KuS8kNP7B-1" target="v5o1fB2vA18KuS8kNP7B-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-28" value="&lt;span style=&quot;font-size: 12px; background-color: rgb(251, 251, 251);&quot;&gt;0：purchaseToken&lt;/span&gt;&lt;br style=&quot;font-size: 12px; background-color: rgb(251, 251, 251);&quot;&gt;&lt;span style=&quot;font-size: 12px; background-color: rgb(251, 251, 251);&quot;&gt;1：collectionsToken&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="v5o1fB2vA18KuS8kNP7B-27" vertex="1" connectable="0">
          <mxGeometry x="-0.2216" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-34" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="v5o1fB2vA18KuS8kNP7B-30" target="v5o1fB2vA18KuS8kNP7B-33" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-30" value="调起支付窗口" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-250" y="390" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-37" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="v5o1fB2vA18KuS8kNP7B-33" target="v5o1fB2vA18KuS8kNP7B-36" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-33" value="支付成功？" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-235" y="475" width="90" height="80" as="geometry" />
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-36" value="等待验证订单通过" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-250" y="610" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-39" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" edge="1" target="v5o1fB2vA18KuS8kNP7B-22">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-40" y="376.56" as="sourcePoint" />
            <mxPoint x="410.00000000000006" y="289.99999999999994" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-40" value="orderParm订单参数（自生成），添加到数据库" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="v5o1fB2vA18KuS8kNP7B-39" vertex="1" connectable="0">
          <mxGeometry x="0.0043" y="1" relative="1" as="geometry">
            <mxPoint x="45" y="-25" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-10" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="v5o1fB2vA18KuS8kNP7B-42" target="8X5j39H2abwFqZzt46MZ-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="v5o1fB2vA18KuS8kNP7B-42" value="&lt;font style=&quot;font-size: 15px;&quot;&gt;VerifyOrder验证订单，&lt;span style=&quot;color: rgba(51, 54, 57, 0.9); font-family: -apple-system, BlinkMacSystemFont, &amp;quot;Segoe UI&amp;quot;, Roboto, &amp;quot;Helvetica Neue&amp;quot;, Arial, &amp;quot;Noto Sans&amp;quot;, sans-serif, &amp;quot;Apple Color Emoji&amp;quot;, &amp;quot;Segoe UI Emoji&amp;quot;; text-align: left; white-space-collapse: preserve; background-color: rgb(255, 255, 255);&quot;&gt;QueryV9 会返回当前用户产品的权利&lt;/span&gt;&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="820" y="500" width="140" height="75" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="8X5j39H2abwFqZzt46MZ-1" target="8X5j39H2abwFqZzt46MZ-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-1" value="现在服务端知道用户userId,知道用户要开通的产品" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="830" y="620" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-2" value="接入自用用户体系,并为用户开通服务" style="whiteSpace=wrap;html=1;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="830" y="740" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-13" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="8X5j39H2abwFqZzt46MZ-11" target="8X5j39H2abwFqZzt46MZ-12">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-11" value="用户自动订阅/关闭订阅取消用户服务" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-750" y="840" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-16" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="8X5j39H2abwFqZzt46MZ-12" target="8X5j39H2abwFqZzt46MZ-15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-12" value="&lt;div style=&quot;text-align: left;&quot;&gt;&lt;font face=&quot;-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji&quot; color=&quot;#000070&quot;&gt;&lt;span style=&quot;font-size: 14px; white-space-collapse: preserve; background-color: rgb(255, 255, 255);&quot;&gt;订单表(Order)存在服务过期时间以及&lt;/span&gt;&lt;/font&gt;&lt;span style=&quot;background-color: initial; text-align: center; font-size: 14px; white-space-collapse: preserve;&quot;&gt;&lt;font face=&quot;-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji&quot; color=&quot;#000070&quot;&gt;宽限期服务时间&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="-544" y="835" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-14" value="微软不支持消耗品进行退款，&lt;br&gt;可提工单进行意外情况处理退款" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="-770" y="790" width="190" height="40" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-20" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="8X5j39H2abwFqZzt46MZ-15" target="8X5j39H2abwFqZzt46MZ-19">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-15" value="当到达过期时间，进入&lt;span style=&quot;color: rgb(0, 0, 112); font-family: -apple-system, BlinkMacSystemFont, &amp;quot;Segoe UI&amp;quot;, Roboto, &amp;quot;Helvetica Neue&amp;quot;, Arial, &amp;quot;Noto Sans&amp;quot;, sans-serif, &amp;quot;Apple Color Emoji&amp;quot;, &amp;quot;Segoe UI Emoji&amp;quot;; font-size: 14px; white-space-collapse: preserve;&quot;&gt;宽限时间，启动定时器查询&lt;/span&gt;&lt;span style=&quot;color: rgba(51, 54, 57, 0.9); font-family: -apple-system, BlinkMacSystemFont, &amp;quot;Segoe UI&amp;quot;, Roboto, &amp;quot;Helvetica Neue&amp;quot;, Arial, &amp;quot;Noto Sans&amp;quot;, sans-serif, &amp;quot;Apple Color Emoji&amp;quot;, &amp;quot;Segoe UI Emoji&amp;quot;; font-size: 14px; text-align: left; white-space-collapse: preserve; background-color: rgb(255, 255, 255);&quot;&gt;RecurrenceQuery&lt;/span&gt;" style="whiteSpace=wrap;html=1;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="-329" y="840" width="159" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-22" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="8X5j39H2abwFqZzt46MZ-19" target="8X5j39H2abwFqZzt46MZ-21">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-23" value="N" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="8X5j39H2abwFqZzt46MZ-22">
          <mxGeometry x="-0.1867" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-25" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="8X5j39H2abwFqZzt46MZ-19" target="8X5j39H2abwFqZzt46MZ-24">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-19" value="AutoRenew" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="-50.5" y="825" width="100.5" height="90" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-21" value="停止服务" style="whiteSpace=wrap;html=1;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="-60.25" y="1010" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-24" value="更新订单表该产品的过期时间，&lt;span style=&quot;color: rgb(0, 0, 112); font-family: -apple-system, BlinkMacSystemFont, &amp;quot;Segoe UI&amp;quot;, Roboto, &amp;quot;Helvetica Neue&amp;quot;, Arial, &amp;quot;Noto Sans&amp;quot;, sans-serif, &amp;quot;Apple Color Emoji&amp;quot;, &amp;quot;Segoe UI Emoji&amp;quot;; font-size: 14px; white-space-collapse: preserve;&quot;&gt;宽限时间，为用户进行服务续费&lt;/span&gt;" style="whiteSpace=wrap;html=1;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="130" y="840" width="139.75" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-28" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="8X5j39H2abwFqZzt46MZ-26" target="8X5j39H2abwFqZzt46MZ-27">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-26" value="一次性产品" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="1360" y="50" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8X5j39H2abwFqZzt46MZ-27" value="通过验证订单，直接履行服务" style="whiteSpace=wrap;html=1;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="1360" y="170" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
