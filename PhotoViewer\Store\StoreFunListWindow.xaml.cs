﻿using ICSharpCode.AvalonEdit;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Windows.Services.Store;

namespace PhotoViewer.Store
{
    public partial class StoreFunListWindow : Window
    {
        private StoreContext storeContext;
        private ObservableCollection<StoreAddOn> addOns;

        [ComImport]
        [Guid("3E68D4BD-7135-4D10-8018-9FB6D9F33FA1")]
        [InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
        public interface IInitializeWithWindow
        { void Initialize(IntPtr hwnd); }

        public StoreFunListWindow()
        {
            InitializeComponent();
            addOns = new ObservableCollection<StoreAddOn>();
            storeContext = GetStoreContext();
            ICSharpCode.AvalonEdit.Search.SearchPanel.Install(jsonEditor.TextArea);
        }

        private StoreContext GetStoreContext()
        {
            if (storeContext == null)
            {
                this.storeContext = StoreContext.GetDefault();
                IInitializeWithWindow initWindow = (IInitializeWithWindow)(object)storeContext;
                initWindow.Initialize(System.Diagnostics.Process.GetCurrentProcess().MainWindowHandle);
            }

            return storeContext;
        }

        private async void Button_Click(object sender, RoutedEventArgs e)
        {
            if (sender == GotoProInfoButton)
                await RunWithLoading(GetAPPInfoForCurrentApp);
            else if (sender == GotoAddonButton)
                await RunWithLoading(GetAddOnInfo);
            else if (sender == GotoProInfoButtonByStoreIdBtn)
                await RunWithLoading(GetProductInfoForStoreId);
            else if (sender == GetUserCollectionBtn)
                await RunWithLoading(GetUserCollection);
            else if (sender == GetAppLicenseBtn)
                await RunWithLoading(GetLicenseInfo);
            else if (sender == GetPurchaseAddOnBtn)
                await RunWithLoading(PurchaseAddOn);
            else if (sender == GetConsumeAddOnBtn)
                await RunWithLoading(GetConsumeAddOn);
            else if (sender == GetRemainingBalanceBtn)
                await RunWithLoading(GetRemainingBalance);
        }

        private async Task RunWithLoading(Func<Task> func)
        {
            IsEnabled = false;
            loadingOverlay.Visibility = Visibility.Visible;
            try { await func(); }
            catch (Exception ex) { jsonEditor.Text = $"Exception: {ex.Message}"; }
            finally
            {
                IsEnabled = true;
                loadingOverlay.Visibility = Visibility.Collapsed;
            }
        }

        private void DisplayJson(object obj)
        {
            jsonEditor.Visibility = Visibility.Visible;
            jsonEditor.Text = JsonConvert.SerializeObject(obj, Formatting.Indented);
        }

        private async Task GetAPPInfoForCurrentApp()
        {
            var result = await storeContext.GetStoreProductForCurrentAppAsync();
            if (result.Product != null) DisplayJson(result.Product);
            else
            {
                jsonEditor.Text = result.ExtendedError != null ?
                    $"ExtendedError: {result.ExtendedError.Message}" : "无";
            }
        }

        private async Task GetAddOnInfo()
        {
            string[] productKinds = { "Durable", "Consumable", "UnmanagedConsumable" };
            var result = await storeContext.GetAssociatedStoreProductsAsync(new List<string>(productKinds));
            if (result.ExtendedError != null || result.Products.Count == 0) DisplayJsonError(result);
            else foreach (var item in result.Products) DisplayJson(item.Value);
        }

        private async Task GetProductInfoForStoreId()
        {
            string[] storeIds = { StoreIdTxt.Text };
            var result = await storeContext.GetStoreProductsAsync(new List<string> { "Application" }, storeIds);
            if (result.ExtendedError != null || result.Products.Count == 0) DisplayJsonError(result);
            else foreach (var item in result.Products) DisplayJson(item.Value);
        }

        private async Task GetUserCollection()
        {
            var result = await storeContext.GetUserCollectionAsync(new List<string> { "Durable" });
            if (result.ExtendedError != null || result.Products.Count == 0) DisplayJsonError(result);
            else foreach (var item in result.Products) AddJsonEditor(item.Value);
        }

        private async Task GetLicenseInfo()
        {
            var license = await storeContext.GetAppLicenseAsync();
            if (license == null) jsonEditor.Text = "An error occurred while retrieving the license.";
            else
            {
                DisplayJson(license);
                foreach (var item in license.AddOnLicenses) AddJsonEditor(item.Value);
            }
        }

        /// <summary>
        /// 支持应用内购买应用和加载项
        /// </summary>
        /// <returns></returns>
        private async Task PurchaseAddOn()
        {
            if (string.IsNullOrEmpty(purchaseAddOnStoreIdTxt.Text)) { MessageBox.Show("Store ID不能为空！"); return; }
            var result = await storeContext.RequestPurchaseAsync(purchaseAddOnStoreIdTxt.Text);
            // Replace the switch expression with a traditional switch statement to make it compatible with C# 7.3.
            string msg;
            switch (result.Status)
            {
                case StorePurchaseStatus.AlreadyPurchased:
                    msg = "The user has already purchased the product.";
                    break;

                case StorePurchaseStatus.Succeeded:
                    msg = "The purchase was successful.";
                    break;

                case StorePurchaseStatus.NotPurchased:
                    msg = "The purchase did not complete. The user may have cancelled the purchase.";
                    break;

                case StorePurchaseStatus.NetworkError:
                    msg = "Network error occurred.";
                    break;

                case StorePurchaseStatus.ServerError:
                    msg = "Server error occurred.";
                    break;

                default:
                    msg = "Unknown error occurred.";
                    break;
            }
            jsonEditor.Text = $"{msg} ExtendedError: {result.ExtendedError?.Message}";
            jsonEditor.Text = $"{msg} ExtendedError: {result.ExtendedError?.Message}";
        }

        /// <summary>
        /// 将可消耗加载项报告为已完成
        /// </summary>
        /// <returns></returns>
        private async Task GetConsumeAddOn()
        {
            if (string.IsNullOrEmpty(addOnStoreIdTxt.Text)) { MessageBox.Show("Store ID不能为空！"); return; }
            if (string.IsNullOrEmpty(addOnQuantityTxt.Text) || !uint.TryParse(addOnQuantityTxt.Text, out var quantity))
            {
                MessageBox.Show("使用数量不能为空或格式不正确！"); return;
            }
            // This is an example for a Store-managed consumable, where you specify the actual number
            // of units that you want to report as consumed so the Store can update the remaining
            // balance. For a developer-managed consumable where you maintain the balance, specify 1
            // to just report the add-on as fulfilled to the Store.
            Guid trackingId = Guid.NewGuid();

            StoreConsumableResult result = await storeContext.ReportConsumableFulfillmentAsync(
                addOnStoreIdTxt.Text, quantity, trackingId);

            // Capture the error message for the operation, if any.
            string extendedError = string.Empty;
            if (result.ExtendedError != null)
            {
                extendedError = result.ExtendedError.Message;
            }

            string msg;

            switch (result.Status)
            {
                case StoreConsumableStatus.Succeeded:
                    msg = "The fulfillment was successful. " +
                        $"Remaining balance: {result.BalanceRemaining}";
                    break;

                case StoreConsumableStatus.InsufficentQuantity:
                    msg = "The fulfillment was unsuccessful because the remaining " +
                        $"balance is insufficient. Remaining balance: {result.BalanceRemaining}";
                    break;

                case StoreConsumableStatus.NetworkError:
                    msg = "The fulfillment was unsuccessful due to a network error. " +
                        "ExtendedError: " + extendedError;
                    break;

                case StoreConsumableStatus.ServerError:
                    msg = "The fulfillment was unsuccessful due to a server error. " +
                        "ExtendedError: " + extendedError;
                    break;

                default:
                    msg = "The fulfillment was unsuccessful due to an unknown error. " +
                        "ExtendedError: " + extendedError;
                    break;
            }

            jsonEditor.Text = msg;
        }

        /// <summary>
        /// 获取应用商店管理的易耗品的剩余余额
        /// </summary>
        /// <returns></returns>
        private async Task GetRemainingBalance()
        {
            if (string.IsNullOrEmpty(addOnStoreIdTxt.Text)) { MessageBox.Show("Store ID不能为空！"); return; }

            StoreConsumableResult result = await storeContext.GetConsumableBalanceRemainingAsync(addOnStoreIdTxt.Text);

            // Capture the error message for the operation, if any.
            string extendedError = string.Empty;
            if (result.ExtendedError != null)
            {
                extendedError = result.ExtendedError.Message;
            }

            string msg;

            switch (result.Status)
            {
                case StoreConsumableStatus.Succeeded:
                    msg = "Remaining balance: " + result.BalanceRemaining;
                    break;

                case StoreConsumableStatus.NetworkError:
                    msg = "Could not retrieve balance due to a network error. " +
                        "ExtendedError: " + extendedError;
                    break;

                case StoreConsumableStatus.ServerError:
                    msg = "Could not retrieve balance due to a server error. " +
                        "ExtendedError: " + extendedError;
                    break;

                default:
                    msg = "Could not retrieve balance due to an unknown error. " +
                        "ExtendedError: " + extendedError;
                    break;
            }

            jsonEditor.Text = msg;
        }

        private void DisplayJsonError(StoreProductQueryResult result)
        {
            jsonEditor.Text = result.ExtendedError != null ?
                $"ExtendedError: {result.ExtendedError.Message}" : "无";
        }

        private void AddJsonEditor(object obj)
        {
            var editor = new TextEditor
            {
                FontFamily = new FontFamily("Consolas"),
                FontSize = 12,
                ShowLineNumbers = true,
                IsReadOnly = true,
                WordWrap = true,
                SyntaxHighlighting = ICSharpCode.AvalonEdit.Highlighting.HighlightingManager.Instance.GetDefinition("Json"),
                Text = JsonConvert.SerializeObject(obj, Formatting.Indented)
            };
            Grid.SetRow(editor, 0);
            Grid.SetColumn(editor, 0);
            EditorGrid.Children.Add(editor);
        }
    }
}