﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Media.Imaging;
using Windows.Services.Store;

namespace PhotoViewer
{
    /// <summary>
    /// SponsorWindow.xaml 的交互逻辑
    /// </summary>
    public partial class SponsorWindow : Window
    {
        private StoreContext storeContext;
        private ObservableCollection<StoreAddOn> addOns;

        public SponsorWindow()
        {
            InitializeComponent();
            addOns = new ObservableCollection<StoreAddOn>();
            AddOnsListBox.ItemsSource = addOns;
            GetAppInfo();
        }

        [ComImport]
        [Guid("3E68D4BD-7135-4D10-8018-9FB6D9F33FA1")]
        [InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
        public interface IInitializeWithWindow
        {
            void Initialize(IntPtr hwnd);
        }

        private StoreContext GetStoreContext()
        {
            if (storeContext == null)
            {
                this.storeContext = StoreContext.GetDefault();
                IInitializeWithWindow initWindow = (IInitializeWithWindow)(object)storeContext;
                initWindow.Initialize(System.Diagnostics.Process.GetCurrentProcess().MainWindowHandle);
            }

            return storeContext;
        }

        public async void GetAppInfo()
        {
            var context = GetStoreContext();

            // Get app store product details. Because this might take several moments,
            // display a ProgressRing during the operation.
            //workingProgressRing.IsActive = true;
            StoreProductResult queryResult = await context.GetStoreProductForCurrentAppAsync();
            //workingProgressRing.IsActive = false;

            if (queryResult.Product == null)
            {
                // The Store catalog returned an unexpected result.
                textBlock.Text = "Something went wrong, and the product was not returned.";

                // Show additional error info if it is available.
                if (queryResult.ExtendedError != null)
                {
                    textBlock.Text += $"\nExtendedError: {queryResult.ExtendedError.Message}";
                }

                return;
            }

            // Display the price of the app.
            textBlock.Text = $"Title: {queryResult.Product.Title}";
            textBlock.Text += $"\nStoreId: {queryResult.Product.StoreId}";
            textBlock.Text += $"\nProductKind: {queryResult.Product.ProductKind}";
            textBlock.Text += $"\nHasDigitalDownload: {queryResult.Product.HasDigitalDownload}";
            textBlock.Text += $"\nThe price of this app is: {queryResult.Product.Price.FormattedBasePrice}";
            textBlock.Text += $"\nDescription: {queryResult.Product.Description}";

            SponsorImage.Source = new BitmapImage(queryResult.Product.Images[0].Uri);
        }

        private async void LoadAddOns()
        {
            try
            {
                //初始化 StoreContext
                storeContext = GetStoreContext();

                string[] productKinds = { "Durable", "Consumable", "UnmanagedConsumable" };
                List<String> filterList = new List<string>(productKinds);

                StoreProductQueryResult queryResult = storeContext.GetAssociatedStoreProductsAsync(filterList).GetResults();

                if (queryResult.ExtendedError != null)
                {
                    MessageBox.Show($"获取加载项时出错: {queryResult.ExtendedError.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                addOns.Clear();
                foreach (var item in queryResult.Products)
                {
                    addOns.Add(new StoreAddOn
                    {
                        Title = item.Value.Title,
                        Description = item.Value.Description,
                        Price = item.Value.Price.FormattedPrice,
                        StoreProduct = item.Value
                    });
                }

                if (addOns.Count == 0)
                {
                    // 如果没有找到加载项，显示模拟数据
                    var mockAddOns = GetMockAddOns();
                    foreach (var addOn in mockAddOns)
                    {
                        addOns.Add(addOn);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载加载项时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 出错时显示模拟数据
                var mockAddOns = GetMockAddOns();
                foreach (var addOn in mockAddOns)
                {
                    addOns.Add(addOn);
                }
            }
        }

        private List<StoreAddOn> GetMockAddOns()
        {
            // 返回模拟的加载项数据
            return new List<StoreAddOn>
            {
                new StoreAddOn { Title = "高级滤镜包", Description = "20种专业级照片滤镜", Price = "￥19.99" },
                new StoreAddOn { Title = "云存储服务", Description = "100GB云存储空间，自动同步您的照片", Price = "￥29.99/年" },
                new StoreAddOn { Title = "人脸识别组件", Description = "智能人脸识别和自动分类功能", Price = "￥15.99" }
            };
        }
    }

    // 表示商店加载项的类
    public class StoreAddOn
    {
        public string Title { get; set; }
        public string Description { get; set; }
        public string Price { get; set; }
        public StoreProduct StoreProduct { get; set; }
    }
}