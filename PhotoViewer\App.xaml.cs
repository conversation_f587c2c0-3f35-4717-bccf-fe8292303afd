﻿using System.IO;
using System.Windows;

namespace PhotoViewer
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // 处理命令行参数
            if (e.Args.Length > 0)
            {
                string filePath = e.Args[0];
                if (File.Exists(filePath))
                {
                    var mainWindow = new MainWindow();
                    mainWindow.LoadImage(filePath);
                    mainWindow.Show();
                    return;
                }
            }

            // 没有参数时正常启动
            new MainWindow().Show();
        }

        protected override void OnExit(ExitEventArgs e)
        {
            // 这里可以添加退出时的清理代码
            // 例如，保存设置、释放资源等
            base.OnExit(e);
        }
    }
}