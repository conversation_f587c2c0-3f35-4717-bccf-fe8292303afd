﻿<Window x:Class="PhotoViewer.SettingWindow.FormatAssociationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="格式关联" Height="350" Width="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        WindowStyle="SingleBorderWindow">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <!-- 全选控件 -->
            <RowDefinition Height="Auto" />
            <!-- 图片格式区域 -->
            <RowDefinition Height="*" />
            <!-- 关闭按钮 -->
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- 全选/取消全选复选框 -->
        <CheckBox x:Name="cbSelectAll" Content="全选/取消全选" Margin="0,0,0,10"
                  Checked="cbSelectAll_Checked" Unchecked="cbSelectAll_Unchecked" />

        <!-- 图片格式复选框：采用 WrapPanel，当竖直空间不足时可自动增加新列 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <WrapPanel x:Name="wpFormats" Orientation="Vertical">
                <CheckBox x:Name="cbJpeg" Content="JPEG (.jpg, .jpeg)" Margin="0,5" />
                <CheckBox x:Name="cbPng" Content="PNG (.png)" Margin="0,5" />
                <CheckBox x:Name="cbBmp" Content="Bitmap (.bmp)" Margin="0,5" />
                <CheckBox x:Name="cbGif" Content="GIF (.gif)" Margin="0,5" />
                <!-- 可根据需要增加更多格式 -->
            </WrapPanel>
        </ScrollViewer>

        <!-- 底部只有一个关闭按钮 -->
        <Button Grid.Row="2" x:Name="btnClose" Content="关闭" Width="80"
                HorizontalAlignment="Right" Margin="0,10,0,0"
                Click="btnClose_Click" />
    </Grid>
</Window>