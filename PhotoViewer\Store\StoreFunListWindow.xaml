﻿<Window x:Class="PhotoViewer.Store.StoreFunListWindow"
       xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
       xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
       xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
       xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
       xmlns:local="clr-namespace:PhotoViewer.Store"
       mc:Ignorable="d"
       WindowStartupLocation="CenterOwner"
       WindowStyle="ToolWindow"
       ResizeMode="NoResize"
       ShowInTaskbar="False"
       Title="Windows Store Fun Demo" Height="450" Width="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition MaxWidth="260" Width="*"></ColumnDefinition>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*"></ColumnDefinition>
        </Grid.ColumnDefinitions>

        <ScrollViewer Grid.Row="0" Grid.Column="0"
           Height="Auto"
           VerticalScrollBarVisibility="Auto"
           Padding="10"
           Background="#F1F1F1">
            <StackPanel>
                <ContentControl Content="功能列表" FontWeight="Bold" />
                <GroupBox Header="获取应用和加载项的产品信息" Margin="0 5" Padding="5">
                    <StackPanel>
                        <Button x:Name="GotoProInfoButton" Content="当前应用的信息" Margin="0,5,0,0" Click="Button_Click" />
                        <Button x:Name="GotoAddonButton" Content="获取加载项的信息" Margin="0,5,0,0" Click="Button_Click" />
                        <Label Content="Store ID:"></Label>
                        <TextBox
    x:Name="StoreIdTxt"
    VerticalScrollBarVisibility="Auto"
    HorizontalScrollBarVisibility="Auto"
    ToolTip="Store ID" />
                        <Button x:Name="GotoProInfoButtonByStoreIdBtn" Content="帐号下其他应用加载项" Margin="0,5,0,0" Click="Button_Click" />
                    </StackPanel>
                </GroupBox>
                <GroupBox Margin="0 5" Padding="5">
                    <StackPanel>
                        <Button x:Name="GetUserCollectionBtn" Content="用户购买的当前应用的加载项" Margin="0,5,0,0" Click="Button_Click" />
                    </StackPanel>
                </GroupBox>
                <GroupBox Margin="0 5" Padding="5">
                    <StackPanel>
                        <Button x:Name="GetAppLicenseBtn" Content="应用和加载项的许可证信息" Margin="0,5,0,0" Click="Button_Click" />
                    </StackPanel>
                </GroupBox>
                <GroupBox Margin="0 5" Header="支持应用内购买应用和加载项" Padding="5">
                    <StackPanel>
                        <Label Content="Store ID:"></Label>
                        <TextBox
                            x:Name="purchaseAddOnStoreIdTxt"
                            VerticalScrollBarVisibility="Auto"
                            HorizontalScrollBarVisibility="Auto"
                            ToolTip="Store ID" />
                        <Button x:Name="GetPurchaseAddOnBtn" Content="读取" Margin="0,5,0,0" Click="Button_Click" />
                    </StackPanel>
                </GroupBox>
                <GroupBox Margin="0 5" Header="支持购买可消耗加载项">
                    <StackPanel>
                        <Label Content="Store ID:"></Label>
                        <TextBox
                            x:Name="addOnStoreIdTxt"
                            VerticalScrollBarVisibility="Auto"
                            HorizontalScrollBarVisibility="Auto"
                            ToolTip="Store ID" />
                        <Label>使用数量：</Label>
                        <TextBox
                            x:Name="addOnQuantityTxt"
                            VerticalScrollBarVisibility="Auto"
                            HorizontalScrollBarVisibility="Auto"
                            ToolTip="消耗品使用数量"
                            Text="1" />
                        <Button x:Name="GetConsumeAddOnBtn" Content="将可消耗加载项报告为已完成" Margin="0,5,0,0" Click="Button_Click" />
                        <Button x:Name="GetRemainingBalanceBtn" Content="应用商店管理的易耗品的剩余余额" Margin="0,5,0,0" Click="Button_Click" />
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <GridSplitter
            Grid.Column="1"
            Width="1"
            Background="Gray"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Stretch"
            ResizeBehavior="PreviousAndNext" />

        <Grid x:Name="EditorGrid" Grid.Row="0"  Grid.Column="2">
            <avalonEdit:TextEditor Name="jsonEditor"
                                   xmlns:avalonEdit="http://icsharpcode.net/sharpdevelop/avalonedit"
                                   HorizontalAlignment="Stretch"
                                   VerticalAlignment="Stretch"
                                   MinHeight="100"
                                   MinWidth="100"
                                    FontFamily="Consolas"
                                    FontSize="12"
                                    ShowLineNumbers="True"
                                    IsReadOnly="True"
                                    WordWrap="True"
                                    SyntaxHighlighting="Json"
                                    Height="Auto" />
        </Grid>

        <!--<StackPanel x:Name="JsonPanel" Grid.Row="0"  Grid.Column="2" Height="Auto"
                    CanVerticallyScroll="True" CanHorizontallyScroll="True">
            <avalonEdit:TextEditor
                xmlns:avalonEdit="http://icsharpcode.net/sharpdevelop/avalonedit"
                Name="jsonEditor"
                FontFamily="Consolas"
                FontSize="12"
                ShowLineNumbers="True"
                IsReadOnly="True"
                WordWrap="True"
                SyntaxHighlighting="Json"
                Height="Auto" />
        </StackPanel>-->
        <!-- Loading 遮罩层（默认隐藏） -->
        <Grid Grid.Row="0" Grid.RowSpan="1" Grid.ColumnSpan="3" x:Name="loadingOverlay" Background="Transparent" Visibility="Collapsed" Panel.ZIndex="100">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="200" Height="20" />
                <TextBlock Text="Loading..." Foreground="White" HorizontalAlignment="Center" Margin="0,10,0,0" />
            </StackPanel>
        </Grid>
    </Grid>
</Window>