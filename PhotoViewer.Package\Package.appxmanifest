﻿<?xml version="1.0" encoding="utf-8"?>

<Package
  xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
  xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10"
  xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities"
  IgnorableNamespaces="uap rescap">

  <Identity
    Name="Iredwhale.iPhotoViewer"
    Publisher="CN=345481AF-A6DB-40F3-AA8E-DACFC8511B2F"
    Version="*******" />

  <Properties>
    <DisplayName>iPhotoViewer</DisplayName>
    <PublisherDisplayName>Iredwhale</PublisherDisplayName>
    <Logo>Images\StoreLogo.png</Logo>
  </Properties>

  <Dependencies>
    <TargetDeviceFamily Name="Windows.Universal" MinVersion="10.0.0.0" MaxVersionTested="10.0.0.0" />
    <TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.14393.0" MaxVersionTested="10.0.14393.0" />
  </Dependencies>

  <Resources>
    <Resource Language="x-generate"/>
  </Resources>

  <Applications>
    <Application Id="App"
      Executable="$targetnametoken$.exe"
      EntryPoint="$targetentrypoint$">
      <uap:VisualElements
        DisplayName="iPhotoViewer"
        Description="A pure and lightweight image browsing app"
        BackgroundColor="transparent"
        Square150x150Logo="Images\Square150x150Logo.png"
        Square44x44Logo="Images\Square44x44Logo.png">
        <uap:DefaultTile Wide310x150Logo="Images\Wide310x150Logo.png"  Square71x71Logo="Images\SmallTile.png" Square310x310Logo="Images\LargeTile.png">
        </uap:DefaultTile >
        <uap:SplashScreen Image="Images\SplashScreen.png" />
        <uap:InitialRotationPreference>
          <uap:Rotation Preference="landscape"/></uap:InitialRotationPreference>
        <uap:LockScreen BadgeLogo="Images\BadgeLogo.png" Notification="badge"/>
      </uap:VisualElements>
      <Extensions>
        <uap:Extension Category="windows.fileTypeAssociation">
          <uap:FileTypeAssociation Name="imageviewer">
            <uap:SupportedFileTypes>
              <uap:FileType ContentType="image/jpeg">.jpg</uap:FileType>
              <uap:FileType ContentType="image/jpeg">.jpeg</uap:FileType>
              <uap:FileType ContentType="image/png">.png</uap:FileType>
            </uap:SupportedFileTypes>
            <uap:Logo>Photo_400x400.png</uap:Logo>
            <uap:DisplayName>图片查看器</uap:DisplayName>
          </uap:FileTypeAssociation>
        </uap:Extension>
      </Extensions>
    </Application>
  </Applications>

  <Capabilities>
    <rescap:Capability Name="runFullTrust" />
  </Capabilities>
</Package>
