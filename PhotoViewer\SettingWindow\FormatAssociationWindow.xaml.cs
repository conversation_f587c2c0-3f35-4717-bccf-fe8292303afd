﻿using Microsoft.Win32;
using System;
using System.Windows;

namespace PhotoViewer.SettingWindow
{
    public partial class FormatAssociationWindow : Window
    {
        public FormatAssociationWindow()
        {
            InitializeComponent();
            // 根据实际需求，可以在初始化时加载当前注册状态
        }

        // 点击“关闭”按钮时，应用选中的关联并关闭窗口
        private void btnClose_Click(object sender, RoutedEventArgs e)
        {
            ApplyFileAssociations();
            this.Close();
        }

        // 根据各复选框的状态进行文件关联注册
        private void ApplyFileAssociations()
        {
            if (cbJpeg.IsChecked == true)
            {
                RegisterFileAssociation(".jpg");
                RegisterFileAssociation(".jpeg");
            }
            if (cbPng.IsChecked == true)
            {
                RegisterFileAssociation(".png");
            }
            if (cbBmp.IsChecked == true)
            {
                RegisterFileAssociation(".bmp");
            }
            if (cbGif.IsChecked == true)
            {
                RegisterFileAssociation(".gif");
            }

            MessageBox.Show("文件关联已更新，部分系统可能需要注销或重启后生效。",
                            "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // 通过注册表在 HKCU 下为指定扩展名注册文件关联
        private void RegisterFileAssociation(string extension)
        {
            try
            {
                // 获取当前应用程序执行文件路径
                string applicationPath = System.Reflection.Assembly.GetExecutingAssembly().Location;
                // 定义一个唯一的 ProgID，例如：PhotoViewer.jpg
                string progId = "PhotoViewer" + extension;

                // 在 HKCU\Software\Classes 下创建或打开指定扩展名的键，并设置默认值为 ProgID
                using (RegistryKey extKey = Registry.CurrentUser.CreateSubKey(@"Software\Classes\" + extension))
                {
                    extKey.SetValue("", progId);
                }

                // 在 HKCU\Software\Classes 下为 ProgID 创建键，并设置显示名称和 open 命令
                using (RegistryKey progKey = Registry.CurrentUser.CreateSubKey(@"Software\Classes\" + progId))
                {
                    progKey.SetValue("", "PhotoViewer Image");
                    using (RegistryKey shellKey = progKey.CreateSubKey(@"shell\open\command"))
                    {
                        // "%1" 表示传递文件路径参数
                        shellKey.SetValue("", $"\"{applicationPath}\" \"%1\"");
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"注册文件关联失败: {ex.Message}",
                                "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 全选复选框选中时，设置所有格式项为选中状态
        private void cbSelectAll_Checked(object sender, RoutedEventArgs e)
        {
            SetAllFormats(true);
        }

        // 全选复选框取消选中时，取消所有格式项的选中状态
        private void cbSelectAll_Unchecked(object sender, RoutedEventArgs e)
        {
            SetAllFormats(false);
        }

        private void SetAllFormats(bool isChecked)
        {
            cbJpeg.IsChecked = isChecked;
            cbPng.IsChecked = isChecked;
            cbBmp.IsChecked = isChecked;
            cbGif.IsChecked = isChecked;
        }
    }
}