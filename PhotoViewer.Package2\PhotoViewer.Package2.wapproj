<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition="'$(VisualStudioVersion)' == '' or '$(VisualStudioVersion)' &lt; '15.0'">
    <VisualStudioVersion>15.0</VisualStudioVersion>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x86">
      <Configuration>Debug</Configuration>
      <Platform>x86</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x86">
      <Configuration>Release</Configuration>
      <Platform>x86</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|ARM">
      <Configuration>Debug</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM">
      <Configuration>Release</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|ARM64">
      <Configuration>Debug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM64">
      <Configuration>Release</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|AnyCPU">
      <Configuration>Debug</Configuration>
      <Platform>AnyCPU</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|AnyCPU">
      <Configuration>Release</Configuration>
      <Platform>AnyCPU</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup>
    <WapProjPath Condition="'$(WapProjPath)'==''">$(MSBuildExtensionsPath)\Microsoft\DesktopBridge\</WapProjPath>
  </PropertyGroup>
  <Import Project="$(WapProjPath)\Microsoft.DesktopBridge.props" />
  <PropertyGroup>
    <ProjectGuid>c8236d0f-6dfe-4a82-8783-c0753bdefba5</ProjectGuid>
    <TargetPlatformVersion>10.0.26100.0</TargetPlatformVersion>
    <TargetPlatformMinVersion>10.0.14393.0</TargetPlatformMinVersion>
    <DefaultLanguage>zh-CN</DefaultLanguage>
    <AppxPackageSigningEnabled>false</AppxPackageSigningEnabled>
    <NoWarn>$(NoWarn);NU1702</NoWarn>
    <GenerateTemporaryStoreCertificate>True</GenerateTemporaryStoreCertificate>
    <EntryPointProjectUniqueName>..\PhotoViewer\PhotoViewer.csproj</EntryPointProjectUniqueName>
  </PropertyGroup>
  <ItemGroup>
    <AppxManifest Include="Package.appxmanifest">
      <SubType>Designer</SubType>
    </AppxManifest>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Images\LargeTile.scale-100.png" />
    <Content Include="Images\LargeTile.scale-125.png" />
    <Content Include="Images\LargeTile.scale-150.png" />
    <Content Include="Images\LargeTile.scale-200.png" />
    <Content Include="Images\LargeTile.scale-400.png" />
    <Content Include="Images\SmallTile.scale-100.png" />
    <Content Include="Images\SmallTile.scale-125.png" />
    <Content Include="Images\SmallTile.scale-150.png" />
    <Content Include="Images\SmallTile.scale-200.png" />
    <Content Include="Images\SmallTile.scale-400.png" />
    <Content Include="Images\SplashScreen.scale-100.png" />
    <Content Include="Images\SplashScreen.scale-125.png" />
    <Content Include="Images\SplashScreen.scale-150.png" />
    <Content Include="Images\SplashScreen.scale-200.png" />
    <Content Include="Images\LockScreenLogo.scale-200.png" />
    <Content Include="Images\SplashScreen.scale-400.png" />
    <Content Include="Images\Square150x150Logo.scale-100.png" />
    <Content Include="Images\Square150x150Logo.scale-125.png" />
    <Content Include="Images\Square150x150Logo.scale-150.png" />
    <Content Include="Images\Square150x150Logo.scale-200.png" />
    <Content Include="Images\Square150x150Logo.scale-400.png" />
    <Content Include="Images\Square44x44Logo.altform-lightunplated_targetsize-16.png" />
    <Content Include="Images\Square44x44Logo.altform-lightunplated_targetsize-24.png" />
    <Content Include="Images\Square44x44Logo.altform-lightunplated_targetsize-256.png" />
    <Content Include="Images\Square44x44Logo.altform-lightunplated_targetsize-32.png" />
    <Content Include="Images\Square44x44Logo.altform-lightunplated_targetsize-48.png" />
    <Content Include="Images\Square44x44Logo.altform-unplated_targetsize-16.png" />
    <Content Include="Images\Square44x44Logo.altform-unplated_targetsize-256.png" />
    <Content Include="Images\Square44x44Logo.altform-unplated_targetsize-32.png" />
    <Content Include="Images\Square44x44Logo.altform-unplated_targetsize-48.png" />
    <Content Include="Images\Square44x44Logo.scale-100.png" />
    <Content Include="Images\Square44x44Logo.scale-125.png" />
    <Content Include="Images\Square44x44Logo.scale-150.png" />
    <Content Include="Images\Square44x44Logo.scale-200.png" />
    <Content Include="Images\Square44x44Logo.scale-400.png" />
    <Content Include="Images\Square44x44Logo.targetsize-16.png" />
    <Content Include="Images\Square44x44Logo.targetsize-24.png" />
    <Content Include="Images\Square44x44Logo.targetsize-24_altform-unplated.png" />
    <Content Include="Images\Square44x44Logo.targetsize-256.png" />
    <Content Include="Images\Square44x44Logo.targetsize-32.png" />
    <Content Include="Images\Square44x44Logo.targetsize-48.png" />
    <Content Include="Images\StoreLogo.scale-100.png" />
    <Content Include="Images\StoreLogo.scale-125.png" />
    <Content Include="Images\StoreLogo.scale-150.png" />
    <Content Include="Images\StoreLogo.scale-200.png" />
    <Content Include="Images\StoreLogo.scale-400.png" />
    <Content Include="Images\Wide310x150Logo.scale-100.png" />
    <Content Include="Images\Wide310x150Logo.scale-125.png" />
    <Content Include="Images\Wide310x150Logo.scale-150.png" />
    <Content Include="Images\Wide310x150Logo.scale-200.png" />
    <Content Include="Images\Wide310x150Logo.scale-400.png" />
    <None Include="Package.StoreAssociation.xml" />
  </ItemGroup>
  <Import Project="$(WapProjPath)\Microsoft.DesktopBridge.targets" />
  <ItemGroup>
    <PackageReference Include="Microsoft.Windows.SDK.BuildTools" Version="10.0.26100.1742" PrivateAssets="all" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\PhotoViewer\PhotoViewer.csproj" />
  </ItemGroup>
</Project>