﻿using System;
using System.Diagnostics;
using System.Security.Principal;
using System.Windows;
using System.Runtime.InteropServices;

namespace PhotoViewer
{
    using System;
    using System.Diagnostics;
    using System.IO;
    using System.Security.Principal;
    using System.Windows;

    public static class AdminCheckHelper
    {
        public static bool IsRunAsAdministrator()
        {
            var wi = WindowsIdentity.GetCurrent();
            var wp = new WindowsPrincipal(wi);
            return wp.IsInRole(WindowsBuiltInRole.Administrator);
        }

        public static void RelaunchAsStandardUser()
        {
            try
            {
                string exePath = Process.GetCurrentProcess().MainModule.FileName;

                ProcessStartInfo psi = new ProcessStartInfo
                {
                    FileName = "explorer.exe",
                    Arguments = $"\"{exePath}\"",
                    UseShellExecute = true,
                    Verb = "", // 不使用 "runas"
                    WindowStyle = ProcessWindowStyle.Normal
                };

                Process.Start(psi);
                Application.Current.Shutdown();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重启失败：{ex.Message}", "错误");
            }
        }
    }
}